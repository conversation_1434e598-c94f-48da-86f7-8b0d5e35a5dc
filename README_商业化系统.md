# 梦羽AI绘图工具 - 商业化版本

## 系统概述

本系统已成功升级为商业化版本，添加了完整的用户注册登录系统和积分扣费机制。每次生成图像或视频都会消耗1积分，实现了商业化运营的基础功能。

## 主要功能

### 1. 用户系统
- **用户注册**: 新用户注册即获得10积分
- **用户登录**: 安全的密码加密和会话管理
- **用户信息**: 显示当前积分、生成历史等
- **权限控制**: 只有登录用户才能使用生成功能

### 2. 积分系统
- **积分消耗**: 每次生成图像或视频消耗1积分
- **积分查询**: 实时显示当前积分余额
- **积分记录**: 完整的积分交易历史
- **积分充值**: 管理员可为用户充值积分

### 3. 管理员功能
- **用户管理**: 查看所有用户信息
- **积分管理**: 为用户充值积分
- **系统统计**: 查看使用统计和排行榜
- **数据监控**: 实时监控系统运行状态

## 快速开始

### 1. 系统初始化
```bash
# 运行初始化脚本创建管理员账户
python init_admin.py
```

### 2. 启动应用
```bash
# 启动Flask应用
python app.py
```

### 3. 访问系统
打开浏览器访问: http://localhost:7799

## 默认账户信息

### 管理员账户
- **用户名**: admin
- **密码**: admin123
- **积分**: 1000
- **权限**: 管理员

### 测试用户账户
- **用户名**: user1, user2, user3
- **密码**: 123456
- **积分**: 10
- **权限**: 普通用户

⚠️ **重要**: 请及时修改默认密码！

## 文件结构

```
├── app.py                 # 主应用文件
├── auth.py               # 用户认证模块
├── points_system.py      # 积分系统模块
├── init_admin.py         # 系统初始化脚本
├── users.json           # 用户数据文件（自动生成）
├── points_data.json     # 积分数据文件（自动生成）
├── templates/
│   └── index.html       # 前端界面
└── static/
    └── images/          # 图片存储目录
```

## 数据存储

系统使用JSON文件进行本地数据存储：

### users.json
存储用户信息，包括：
- 用户名和加密密码
- 邮箱地址
- 积分余额
- 注册时间和最后登录时间
- 生成历史记录
- 管理员权限标识

### points_data.json
存储积分系统数据，包括：
- 积分交易记录
- 系统设置（生成消耗、奖励等）
- 统计信息

## 安全特性

1. **密码加密**: 使用PBKDF2算法加密存储密码
2. **会话管理**: Flask session管理用户登录状态
3. **权限控制**: 装饰器实现的权限验证
4. **数据备份**: 自动创建数据文件备份

## API接口

### 用户认证
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `GET /user_info` - 获取用户信息

### 内容生成
- `POST /generate` - 生成图像（需要登录）
- `POST /generate_video` - 生成视频（需要登录）

### 管理员功能
- `GET /admin/users` - 获取用户列表
- `POST /admin/add_points` - 为用户充值积分
- `GET /admin/statistics` - 获取系统统计

## 使用流程

1. **新用户注册**
   - 点击"注册"按钮
   - 填写用户名、密码（可选邮箱）
   - 自动获得10积分

2. **用户登录**
   - 点击"登录"按钮
   - 输入用户名和密码
   - 登录成功后显示积分余额

3. **生成内容**
   - 输入提示词
   - 选择模型和参数
   - 点击生成（自动扣除1积分）

4. **管理员操作**
   - 使用admin账户登录
   - 点击"管理面板"
   - 进行用户管理和积分充值

## 商业化建议

1. **积分定价策略**
   - 可根据实际成本调整每次生成的积分消耗
   - 设置不同的积分包价格

2. **用户激励**
   - 每日签到奖励
   - 推荐新用户奖励
   - VIP会员制度

3. **功能扩展**
   - 支付接口集成
   - 更详细的使用统计
   - 用户等级系统

## 技术支持

如有问题，请联系：
- QQ: 1031029814
- 邮箱: <EMAIL>

## 更新日志

### v2.0.0 (2025-07-19)
- ✅ 添加用户注册登录系统
- ✅ 实现积分扣费机制
- ✅ 添加管理员后台功能
- ✅ 完善权限控制
- ✅ 优化用户界面
- ✅ 添加数据统计功能

### v1.0.0
- 基础AI绘图功能
- 图像和视频生成
- 代理池支持
