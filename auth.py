import hashlib
import secrets
import json
import os
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect, url_for

class UserManager:
    def __init__(self, data_file='users.json'):
        self.data_file = data_file
        self.users = self.load_users()
    
    def load_users(self):
        """加载用户数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def save_users(self):
        """保存用户数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
    
    def hash_password(self, password):
        """密码加密"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return salt + password_hash.hex()
    
    def verify_password(self, password, hashed_password):
        """验证密码"""
        salt = hashed_password[:32]
        stored_hash = hashed_password[32:]
        password_hash = hashlib.pbkdf2_hmac('sha256',
                                          password.encode('utf-8'),
                                          salt.encode('utf-8'),
                                          100000)
        return password_hash.hex() == stored_hash
    
    def register_user(self, username, password, email=None):
        """用户注册"""
        if username in self.users:
            return False, "用户名已存在"
        
        if len(username) < 3:
            return False, "用户名至少需要3个字符"
        
        if len(password) < 6:
            return False, "密码至少需要6个字符"
        
        # 创建新用户
        user_data = {
            'username': username,
            'password': self.hash_password(password),
            'email': email or '',
            'points': 10,  # 新用户赠送10点积分
            'created_at': datetime.now().isoformat(),
            'last_login': None,
            'is_admin': False,
            'total_generated': 0,  # 总生成次数
            'generation_history': []  # 生成历史
        }
        
        self.users[username] = user_data
        
        if self.save_users():
            return True, "注册成功，赠送10点积分"
        else:
            return False, "注册失败，请重试"
    
    def login_user(self, username, password):
        """用户登录"""
        if username not in self.users:
            return False, "用户名不存在"
        
        user = self.users[username]
        if not self.verify_password(password, user['password']):
            return False, "密码错误"
        
        # 更新最后登录时间
        user['last_login'] = datetime.now().isoformat()
        self.save_users()
        
        return True, "登录成功"
    
    def get_user(self, username):
        """获取用户信息"""
        return self.users.get(username)
    
    def update_user_points(self, username, points_change):
        """更新用户积分"""
        if username not in self.users:
            return False, "用户不存在"
        
        user = self.users[username]
        new_points = user['points'] + points_change
        
        if new_points < 0:
            return False, "积分不足"
        
        user['points'] = new_points
        self.save_users()
        return True, f"积分更新成功，当前积分：{new_points}"
    
    def add_generation_record(self, username, generation_type, prompt, success=True):
        """添加生成记录"""
        if username not in self.users:
            return False
        
        user = self.users[username]
        record = {
            'type': generation_type,  # 'image' or 'video'
            'prompt': prompt[:100],  # 只保存前100个字符
            'timestamp': datetime.now().isoformat(),
            'success': success
        }
        
        user['generation_history'].append(record)
        
        # 只保留最近100条记录
        if len(user['generation_history']) > 100:
            user['generation_history'] = user['generation_history'][-100:]
        
        if success:
            user['total_generated'] += 1
        
        self.save_users()
        return True
    
    def get_user_stats(self, username):
        """获取用户统计信息"""
        if username not in self.users:
            return None
        
        user = self.users[username]
        recent_history = user['generation_history'][-10:]  # 最近10条记录
        
        return {
            'username': username,
            'points': user['points'],
            'total_generated': user['total_generated'],
            'created_at': user['created_at'],
            'last_login': user['last_login'],
            'recent_history': recent_history
        }

# 装饰器：要求用户登录
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：要求管理员权限
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'username' not in session:
            if request.is_json:
                return jsonify({'success': False, 'message': '请先登录'}), 401
            return redirect(url_for('login'))
        
        # 这里需要在app.py中初始化user_manager后才能使用
        # 暂时简化处理
        return f(*args, **kwargs)
    return decorated_function

# 装饰器：检查积分是否足够
def points_required(points_needed=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'username' not in session:
                if request.is_json:
                    return jsonify({'success': False, 'message': '请先登录'}), 401
                return redirect(url_for('login'))
            
            # 这里需要在具体使用时检查积分
            return f(*args, **kwargs)
        return decorated_function
    return decorator
